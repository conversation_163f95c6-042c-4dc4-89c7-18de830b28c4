<?if $(sys.B<PERSON><PERSON>ARCH)="x86"?>
    <?define Win64 = "no" ?>
    <?define PlatformProgramFilesFolder = "ProgramFilesFolder" ?>
<?elseif $(sys.BUILDARCH)="x64"?>
    <?define Win64 = "yes" ?>
    <?define PlatformProgramFilesFolder = "ProgramFiles64Folder" ?>
<?elseif $(sys.BUILDARCH)="arm64"?>
    <?define Win64 = "yes" ?>
    <?define PlatformProgramFilesFolder = "ProgramFiles64Folder" ?>
<?else?>
    <?error Unsupported value of sys.BUILDARCH=$(sys.BUILDARCH)?>
<?endif?>

<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">
    <Product
            Id="*"
            Name="RealityTap Haptics Studio"
            UpgradeCode="dfa82347-b28e-549b-8f38-eb7559a97d0a"
            Language="!(loc.TauriLanguage)"
            Manufacturer="AWA"
            Version="1.0.8">

        <Package Id="*"
                 Keywords="Installer"
                 InstallerVersion="450"
                 Languages="0"
                 Compressed="yes"
                 InstallScope="perMachine"
                 SummaryCodepage="!(loc.TauriCodepage)"/>

        <!-- https://docs.microsoft.com/en-us/windows/win32/msi/reinstallmode -->
        <!-- reinstall all files; rewrite all registry entries; reinstall all shortcuts -->
        <Property Id="REINSTALLMODE" Value="amus" />

        <!-- Auto launch app after installation, useful for passive mode which usually used in updates -->
        <Property Id="AUTOLAUNCHAPP" Secure="yes" />
        <!-- Property to forward cli args to the launched app to not lose those of the pre-update instance -->
        <Property Id="LAUNCHAPPARGS" Secure="yes" />

            <MajorUpgrade Schedule="afterInstallInitialize" AllowDowngrades="yes" />

        <InstallExecuteSequence>
            <RemoveShortcuts>Installed AND NOT UPGRADINGPRODUCTCODE</RemoveShortcuts>
        </InstallExecuteSequence>

        <Media Id="1" Cabinet="app.cab" EmbedCab="yes" />


        <Icon Id="ProductIcon" SourceFile="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\target\release\resources\icon.ico"/>
        <Property Id="ARPPRODUCTICON" Value="ProductIcon" />
        <Property Id="ARPNOREPAIR" Value="yes" Secure="yes" />      <!-- Remove repair -->
        <SetProperty Id="ARPNOMODIFY" Value="1" After="InstallValidate" Sequence="execute"/>


        <Property Id="INSTALLDIR">
          <!-- First attempt: Search for "InstallDir" -->
          <RegistrySearch Id="PrevInstallDirWithName" Root="HKCU" Key="Software\AWA\RealityTap Haptics Studio" Name="InstallDir" Type="raw" />

          <!-- Second attempt: If the first fails, search for the default key value (this is how the nsis installer currently stores the path) -->
          <RegistrySearch Id="PrevInstallDirNoName" Root="HKCU" Key="Software\AWA\RealityTap Haptics Studio" Type="raw" />
        </Property>

        <!-- launch app checkbox -->
        <Property Id="WIXUI_EXITDIALOGOPTIONALCHECKBOXTEXT" Value="!(loc.LaunchApp)" />
        <Property Id="WIXUI_EXITDIALOGOPTIONALCHECKBOX" Value="1"/>
        <CustomAction Id="LaunchApplication" Impersonate="yes" FileKey="Path" ExeCommand="[LAUNCHAPPARGS]" Return="asyncNoWait" />

        <UI>
            <!-- launch app checkbox -->
            <Publish Dialog="ExitDialog" Control="Finish" Event="DoAction" Value="LaunchApplication">WIXUI_EXITDIALOGOPTIONALCHECKBOX = 1 and NOT Installed</Publish>

            <Property Id="WIXUI_INSTALLDIR" Value="INSTALLDIR" />

            <!-- Skip license dialog -->
            <Publish Dialog="WelcomeDlg"
                     Control="Next"
                     Event="NewDialog"
                     Value="InstallDirDlg"
                     Order="2">1</Publish>
            <Publish Dialog="InstallDirDlg"
                     Control="Back"
                     Event="NewDialog"
                     Value="WelcomeDlg"
                     Order="2">1</Publish>
        </UI>

        <UIRef Id="WixUI_InstallDir" />

        <Directory Id="TARGETDIR" Name="SourceDir">
            <Directory Id="DesktopFolder" Name="Desktop">
                <Component Id="ApplicationShortcutDesktop" Guid="*">
                    <Shortcut Id="ApplicationDesktopShortcut" Name="RealityTap Haptics Studio" Description="Runs RealityTap Haptics Studio" Target="[!Path]" WorkingDirectory="INSTALLDIR" />
                    <RemoveFolder Id="DesktopFolder" On="uninstall" />
                    <RegistryValue Root="HKCU" Key="Software\AWA\RealityTap Haptics Studio" Name="Desktop Shortcut" Type="integer" Value="1" KeyPath="yes" />
                </Component>
            </Directory>
            <Directory Id="$(var.PlatformProgramFilesFolder)" Name="PFiles">
                <Directory Id="INSTALLDIR" Name="RealityTap Haptics Studio"/>
            </Directory>
            <Directory Id="ProgramMenuFolder">
                <Directory Id="ApplicationProgramsFolder" Name="RealityTap Haptics Studio"/>
            </Directory>
        </Directory>

        <DirectoryRef Id="INSTALLDIR">
            <Component Id="RegistryEntries" Guid="*">
                <RegistryKey Root="HKCU" Key="Software\AWA\RealityTap Haptics Studio">
                    <RegistryValue Name="InstallDir" Type="string" Value="[INSTALLDIR]" KeyPath="yes" />
                </RegistryKey>
                <!-- Change the Root to HKCU for perUser installations -->
</Component>
            <Component Id="Path" Guid="0147ef6a-fc30-5fd9-bd41-312c6ae15b1a" Win64="$(var.Win64)">
                <File Id="Path" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\target\release\realitytap_studio.exe" KeyPath="yes" Checksum="yes"/>
</Component>
            <Component Id="I0bf73306b03549d98996e5aad13aa78d" Guid="7290ffde-a336-4c1d-bd77-f8e61053ef8b" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I0bf73306b03549d98996e5aad13aa78d" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\librtcore.dll" /></Component><Component Id="I01d7cbbca9ae4b079a91c3dd9ca26582" Guid="021b41a1-bf96-453e-a152-fa313c34254e" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I01d7cbbca9ae4b079a91c3dd9ca26582" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\librtssl.dll" /></Component><Component Id="Iee621deafee34e3f9a9515456969be1b" Guid="b633ef89-5918-41c4-980a-2182fbf07772" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Iee621deafee34e3f9a9515456969be1b" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\librtutils.dll" /></Component><Component Id="I6a864a4dae1c42028a38a984807f256c" Guid="25c6dbb1-4475-4427-b5ed-5b9279f039bd" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I6a864a4dae1c42028a38a984807f256c" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\libgcc_s_seh-1.dll" /></Component><Component Id="Ie69476f0ad5843198c70da967502ea62" Guid="547bb015-6576-4221-be53-0236d0c862a2" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ie69476f0ad5843198c70da967502ea62" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\libstdc++-6.dll" /></Component><Component Id="Idb63bcbcdd054397b334883a0db05554" Guid="eab9d0cf-dab0-466f-95e4-d40bbec2284f" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Idb63bcbcdd054397b334883a0db05554" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\libwinpthread-1.dll" /></Component><Component Id="I7cfbce1657b9430b83bc8c320941a81d" Guid="78ba1386-a5b3-48f5-a9e2-cf7a3a5efbfb" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I7cfbce1657b9430b83bc8c320941a81d" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\ffmpeg.exe" /></Component><Component Id="I29c38cc48d564f859a2b5ba993c8d7dd" Guid="d703a8d2-c346-4c12-a2a3-27a95664f79b" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I29c38cc48d564f859a2b5ba993c8d7dd" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\ffprobe.exe" /></Component><Directory Id="I5dd5890306674c619c0c50f68f00de4b" Name="motors"><Component Id="I3686eef8585942edb281b387f26cdb9d" Guid="fb913a19-ad59-4e41-909e-0952d71e3ed8" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I3686eef8585942edb281b387f26cdb9d" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\motors\LRA_0809_normal_170Hz.conf" /></Component><Component Id="I117b87d937104671938c45d359410027" Guid="d3397d8d-09d4-458b-bfe6-185da0ae44d4" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I117b87d937104671938c45d359410027" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\motors\LRA_0809_pro_170Hz.conf" /></Component><Component Id="Ia1e3684e55ab4b46a518eb546ac49500" Guid="8a7fc211-7c6d-4992-8b35-f2e681cc68eb" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ia1e3684e55ab4b46a518eb546ac49500" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\motors\LRA_0916_normal_170Hz.conf" /></Component></Directory>
            <Component Id="CMP_UninstallShortcut" Guid="*">

                <Shortcut Id="UninstallShortcut"
						  Name="Uninstall RealityTap Haptics Studio"
						  Description="Uninstalls RealityTap Haptics Studio"
						  Target="[System64Folder]msiexec.exe"
						  Arguments="/x [ProductCode]" />

				<RemoveFolder Id="INSTALLDIR"
							  On="uninstall" />

				<RegistryValue Root="HKCU"
							   Key="Software\AWA\RealityTap Haptics Studio"
							   Name="Uninstaller Shortcut"
							   Type="integer"
							   Value="1"
							   KeyPath="yes" />
            </Component>
        </DirectoryRef>

        <DirectoryRef Id="ApplicationProgramsFolder">
            <Component Id="ApplicationShortcut" Guid="*">
                <Shortcut Id="ApplicationStartMenuShortcut"
                    Name="RealityTap Haptics Studio"
                    Description="Runs RealityTap Haptics Studio"
                    Target="[!Path]"
                    Icon="ProductIcon"
                    WorkingDirectory="INSTALLDIR">
                    <ShortcutProperty Key="System.AppUserModel.ID" Value="com.awa.realitytap.desktop"/>
                </Shortcut>
                <RemoveFolder Id="ApplicationProgramsFolder" On="uninstall"/>
                <RegistryValue Root="HKCU" Key="Software\AWA\RealityTap Haptics Studio" Name="Start Menu Shortcut" Type="integer" Value="1" KeyPath="yes"/>
           </Component>
        </DirectoryRef>

<Feature
                Id="MainProgram"
                Title="Application"
                Description="!(loc.InstallAppFeature)"
                Level="1"
                ConfigurableDirectory="INSTALLDIR"
                AllowAdvertise="no"
                Display="expand"
                Absent="disallow">

            <ComponentRef Id="RegistryEntries"/>

<ComponentRef Id="I0bf73306b03549d98996e5aad13aa78d"/>
<ComponentRef Id="I01d7cbbca9ae4b079a91c3dd9ca26582"/>
<ComponentRef Id="Iee621deafee34e3f9a9515456969be1b"/>
<ComponentRef Id="I6a864a4dae1c42028a38a984807f256c"/>
<ComponentRef Id="Ie69476f0ad5843198c70da967502ea62"/>
<ComponentRef Id="Idb63bcbcdd054397b334883a0db05554"/>
<ComponentRef Id="I7cfbce1657b9430b83bc8c320941a81d"/>
<ComponentRef Id="I29c38cc48d564f859a2b5ba993c8d7dd"/>
<ComponentRef Id="I3686eef8585942edb281b387f26cdb9d"/>
<ComponentRef Id="I117b87d937104671938c45d359410027"/>
<ComponentRef Id="Ia1e3684e55ab4b46a518eb546ac49500"/>

            <Feature Id="ShortcutsFeature"
                Title="Shortcuts"
                Level="1">
                <ComponentRef Id="Path"/>
                <ComponentRef Id="CMP_UninstallShortcut" />
                <ComponentRef Id="ApplicationShortcut" />
                <ComponentRef Id="ApplicationShortcutDesktop" />
            </Feature>

            <Feature
                Id="Environment"
                Title="PATH Environment Variable"
                Description="!(loc.PathEnvVarFeature)"
                Level="1"
                Absent="allow">
            <ComponentRef Id="Path"/>
</Feature>
        </Feature>

        <Feature Id="External" AllowAdvertise="no" Absent="disallow">
</Feature>

        <!-- WebView2 -->
        <Property Id="WVRTINSTALLED">
            <RegistrySearch Id="WVRTInstalledSystem" Root="HKLM" Key="SOFTWARE\Microsoft\EdgeUpdate\Clients\{F3017226-FE2A-4295-8BDF-00C3A9A7E4C5}" Name="pv" Type="raw" Win64="no" />
            <RegistrySearch Id="WVRTInstalledUser" Root="HKCU" Key="SOFTWARE\Microsoft\EdgeUpdate\Clients\{F3017226-FE2A-4295-8BDF-00C3A9A7E4C5}" Name="pv" Type="raw"/>
        </Property>

        <CustomAction Id='DownloadAndInvokeBootstrapper' Directory="INSTALLDIR" Execute="deferred" ExeCommand='powershell.exe -NoProfile -windowstyle hidden try [\{] [\[]Net.ServicePointManager[\]]::SecurityProtocol = [\[]Net.SecurityProtocolType[\]]::Tls12 [\}] catch [\{][\}]; Invoke-WebRequest -Uri "https://go.microsoft.com/fwlink/p/?LinkId=2124703" -OutFile "$env:TEMP\MicrosoftEdgeWebview2Setup.exe" ; Start-Process -FilePath "$env:TEMP\MicrosoftEdgeWebview2Setup.exe" -ArgumentList (&apos;/silent&apos;, &apos;/install&apos;) -Wait' Return='check'/>
        <InstallExecuteSequence>
            <Custom Action='DownloadAndInvokeBootstrapper' Before='InstallFinalize'>
                <![CDATA[NOT(REMOVE OR WVRTINSTALLED)]]>
            </Custom>
        </InstallExecuteSequence>

        <!-- Embedded webview bootstrapper mode -->

        <!-- Embedded offline installer -->



        <InstallExecuteSequence>
          <Custom Action="LaunchApplication" After="InstallFinalize">AUTOLAUNCHAPP AND NOT Installed</Custom>
        </InstallExecuteSequence>

        <SetProperty Id="ARPINSTALLLOCATION" Value="[INSTALLDIR]" After="CostFinalize"/>
    </Product>
</Wix>
